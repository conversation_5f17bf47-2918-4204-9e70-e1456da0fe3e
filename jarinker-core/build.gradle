// Add compiler arguments to access jdeps module
tasks.withType(JavaCompile).configureEach {
    options.compilerArgs.addAll([
            "--add-modules=jdk.jdeps",
            "--add-exports=jdk.jdeps/com.sun.tools.jdeps=ALL-UNNAMED",
    ])
}

test {
    useJUnitPlatform()
    jvmArgs([
            "--add-modules", "jdk.jdeps",
            "--add-exports", "jdk.jdeps/com.sun.tools.jdeps=ALL-UNNAMED",
    ])
}
